from config import Config
import hashlib
import time
import requests


def calculate_sign(timestamp):
    """ 计算签名"""
    sign_str = f"{timestamp}{Config.APP_SECRET}"
    return hashlib.md5(sign_str.encode('utf-8')).hexdigest()


def do_request(url: str, params: dict):
    """
    发送HTTP GET请求
    """
    # 获取当前时间戳
    timestamp = str(int(time.time()))
    sign = calculate_sign(timestamp)

    # 设置请求头和请求参数
    headers = {
        "sign": sign,
        "timestamp": timestamp
    }

    response = requests.get(url, headers=headers, params=params)
    if response.status_code == 200:
        result = response.json()
        if result.get("status") == 200:
            return result
        else:
            return {"status": "error", "message": result.get("message", "查询失败")}
    else:
        return {"status": "error", "message": f"请求失败，状态码：{response.status_code}"}


def get_products(keyword: str):
    """
    通过关键词进行模糊查询产品信息（同步）
    """
    params = {
        "key": keyword,
    }

    # 获取API URL
    url = Config.get_search_url()

    # 发送同步请求
    response = do_request(url, params=params)
    return response


def get_info_by_id(product_id: str):
    """
    根据id查询产品信息（同步）
    """
    params = {
        "id": product_id
    }

    # 获取API URL
    url = Config.get_product_info_url()
    # 发送同步请求
    response = do_request(url, params=params)
    return response


def get_stock_by_id(product_id: str):
    """
    根据id查询库存信息（同步）
    """
    params = {
        "id": product_id
    }

    # 获取API URL
    url = Config.get_product_stock_url()
    # 发送同步请求
    response = do_request(url, params=params)
    return response


def get_price_by_id(product_id: str):
    """
    根据id查询价格信息（同步）
    """
    params = {
        "id": product_id
    }

    # 获取API URL
    url = Config.get_product_price_url()

    # 发送同步请求
    response = do_request(url, params=params)
    return response
