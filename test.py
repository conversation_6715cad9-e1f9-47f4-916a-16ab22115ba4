from config import Config
from utils.query import get_products, get_info_by_id, get_price_by_id, get_stock_by_id

def read_config():
    # 获取模糊查询API的完整URL
    search_url = Config.get_search_url()
    print(f"模糊查询URL: {search_url}")

    # 获取应用密钥
    app_secret = Config.APP_SECRET
    print(f"应用密钥: {app_secret}")





def main():
    product_id = "08e361b6-aea3-43c7-9721-b1a96f2159df"

    # 模糊查询产品
    products = get_products("弓箭")
    print(f"模糊查询结果: {products}")


    product_info = get_info_by_id(product_id)
    print(f"产品信息: {product_info}")


    product_price = get_price_by_id(product_id)
    print(f"产品价格: {product_price}")

    product_stock = get_stock_by_id(product_id)
    print(f"产品库存: {product_stock}")

if __name__ == '__main__':
    # read_config()
    main()