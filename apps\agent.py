from fastapi import FastAPI, Query, HTTPException
from typing import Optional, Dict, Any
from pydantic import BaseModel
import sys
import os

# 添加项目根目录到系统路径，确保可以导入 utils 模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.query import (
    get_products,
    get_info_by_id,
    get_stock_by_id,
    get_price_by_id
)

# 创建 FastAPI 应用实例
app = FastAPI(
    title="Product Query API",
    description="用于查询产品信息、库存和价格的API",
    version="1.0.0",
)

# 定义响应模型
class ResponseModel(BaseModel):
    status: str
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None


@app.get("/products/search/", response_model=ResponseModel, tags=["products"], operation_id="search_products")
def search_products(name: str = Query(..., description="搜索关键词")):
    """
    通过关键词进行模糊查询产品信息

    - **keyword**: 搜索关键词
    """
    try:
        result = get_products(name)
        if "status" in result and result["status"] == "error":
            return {"status": "error", "message": result.get("message", "查询失败")}
        return {"status": "success", "data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询产品信息时发生错误: {str(e)}")


@app.get("/products/{product_id}/info/", response_model=ResponseModel, tags=["products"], operation_id="get_product_info_by_id")
def get_product_info_by_id(product_id: str):
    """
    根据产品ID查询产品详细信息

    - **product_id**: 产品ID
    """
    try:
        result = get_info_by_id(product_id)
        if "status" in result and result["status"] == "error":
            return {"status": "error", "message": result.get("message", "查询失败")}
        return {"status": "success", "data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询产品信息时发生错误: {str(e)}")


@app.get("/products/{product_id}/stock/", response_model=ResponseModel, tags=["products"], operation_id="get_product_stock_by_id")
def get_product_stock_by_id(product_id: str):
    """
    根据产品ID查询产品的库存信息

    - **product_id**: 产品ID
    """
    try:
        result = get_stock_by_id(product_id)
        if "status" in result and result["status"] == "error":
            return {"status": "error", "message": result.get("message", "查询失败")}
        return {"status": "success", "data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询库存信息时发生错误: {str(e)}")


@app.get("/products/{product_id}/price/", response_model=ResponseModel, tags=["products"], operation_id="get_product_price_by_id")
def get_product_price_by_id(product_id: str):
    """
    根据产品ID查询产品的价格信息

    - **product_id**: 产品ID
    """
    try:
        result = get_price_by_id(product_id)
        if "status" in result and result["status"] == "error":
            return {"status": "error", "message": result.get("message", "查询失败")}
        return {"status": "success", "data": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询价格信息时发生错误: {str(e)}")

