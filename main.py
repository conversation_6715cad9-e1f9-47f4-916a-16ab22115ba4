from apps.agent import app
from utils.setup import setup_logging

from fastapi_mcp import FastApiMCP

setup_logging()

# Add MCP server to the FastAPI app
mcp = FastApiMCP(
    app,
    name="API MCP",
    description="MCP server for the APP API",
    describe_full_response_schema=True,
    describe_all_responses=True,
)

mcp.mount()

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8888)
