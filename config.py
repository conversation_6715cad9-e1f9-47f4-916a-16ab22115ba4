import os
from dotenv import load_dotenv

load_dotenv()

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """配置类，读取环境变量中的配置信息"""

    # API基础URL
    API_BASE = os.getenv('MRP_API_BASE')

    # 应用密钥
    APP_SECRET = os.getenv('MRP_APP_SECRET')

    # API路径
    MRP_PRODUCT_SEARCH = os.getenv('MRP_PRODUCT_SEARCH')
    API_PRODUCT_INFO = os.getenv('MRP_PRODUCT_INFO')
    API_PRODUCT_PRICE = os.getenv('MRP_PRODUCT_PRICE')
    API_PRODUCT_STOCK = os.getenv('MRP_PRODUCT_STOCK')

    @classmethod
    def get_full_url(cls, api_path):
        """获取完整的API URL

        Args:
            api_path: API路径

        Returns:
            完整的API URL
        """
        return f"{cls.API_BASE}{api_path}"

    @classmethod
    def get_search_url(cls):
        """获取模糊查询API的完整URL"""
        return cls.get_full_url(cls.MRP_PRODUCT_SEARCH)

    @classmethod
    def get_product_info_url(cls):
        """获取精确查询产品信息API的完整URL"""
        return cls.get_full_url(cls.API_PRODUCT_INFO)

    @classmethod
    def get_product_price_url(cls):
        """获取精确查询产品价格API的完整URL"""
        return cls.get_full_url(cls.API_PRODUCT_PRICE)

    @classmethod
    def get_product_stock_url(cls):
        """获取精确查询产品库存API的完整URL"""
        return cls.get_full_url(cls.API_PRODUCT_STOCK)

