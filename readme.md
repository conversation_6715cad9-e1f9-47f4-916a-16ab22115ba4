# 智能客服系统

智能客服是一个基于自然语言处理和机器学习的机器人，旨在为用户提供商品咨询服务。该系统通过RESTful API接口与产品数据库交互，实现商品信息查询、库存查询和价格查询等功能。

## 项目概述

本项目是一个基于FastAPI框架开发的智能客服系统，主要用于商品信息查询服务。系统能够根据用户输入的关键词或产品ID，从后端数据源获取并返回相关商品的详细信息、库存状态和价格信息。

## 系统架构

项目采用模块化设计，主要包含以下几个部分：

- **API服务**：基于FastAPI框架实现的RESTful API接口
- **查询模块**：负责与外部数据源进行通信并获取产品信息
- **日志系统**：记录系统运行状态和错误信息
- **配置管理**：集中管理系统配置参数

## 功能特点

- **商品模糊搜索**：通过关键词查询相关商品信息
- **商品详情查询**：根据产品ID获取商品的详细信息
- **库存状态查询**：查询指定商品的当前库存状态
- **价格信息查询**：获取指定商品的价格信息
- **API文档**：自动生成的API接口文档，便于集成和使用

## 技术栈

- **后端框架**：FastAPI
- **API文档**：FastAPI-MCP (自动生成API文档)
- **HTTP客户端**：Requests
- **服务器**：Uvicorn
- **环境变量管理**：python-dotenv

## 项目结构

```
chat-agent/
├── main.py              # 主入口文件，启动FastAPI服务
├── config.py            # 配置文件，管理环境变量和API路径
├── requirements.txt     # 项目依赖列表
├── apps/                # 应用模块目录
│   ├── __init__.py
│   ├── agent.py         # API接口定义文件
│   └── items.py         # 数据模型定义
├── utils/               # 工具模块目录
│   ├── __init__.py
│   ├── query.py         # 产品查询功能实现
│   └── setup.py         # 系统初始化设置
└── logs/                # 日志文件目录
```

## 安装与部署

1. 克隆项目代码
2. 安装依赖包：
   ```
   pip install -r requirements.txt
   ```
3. 配置环境变量（创建.env文件）：
   ```
   MRP_API_BASE=http://api.example.com/
   MRP_APP_SECRET=your_secret_key
   MRP_PRODUCT_SEARCH=/api/products/search
   MRP_PRODUCT_INFO=/api/products/info
   MRP_PRODUCT_PRICE=/api/products/price
   MRP_PRODUCT_STOCK=/api/products/stock
   ```
4. 启动服务：
   ```
   python main.py
   ```

## API接口

启动服务后，可以通过以下接口访问系统功能：

- `GET /products/search/` - 通过关键词搜索商品
- `GET /products/{product_id}/info/` - 获取商品详细信息
- `GET /products/{product_id}/stock/` - 查询商品库存
- `GET /products/{product_id}/price/` - 查询商品价格

API文档可通过访问 `http://localhost:8000/docs` 获取。

## 安全性

系统通过时间戳和签名机制确保API调用的安全性，每次请求都会生成基于时间戳和密钥的签名。

## 日志

系统日志存储在 `logs` 目录下，记录系统运行状态和错误信息，便于问题排查和系统监控。
